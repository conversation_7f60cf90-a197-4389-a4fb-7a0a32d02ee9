# Aggie Development Server Startup Guide

## 🚀 Automatisk start (Rekommenderat)

### Windows PowerShell:
```powershell
.\start-dev.ps1
```

### Windows Command Prompt:
```cmd
start-dev.bat
```

### Mac/Linux:
```bash
./start-dev.sh
```

<PERSON><PERSON> script startar automatiskt alla servrar i separata terminaler:
- Backend API (Port 8000)
- App Frontend (Port 3000)
- Admin Frontend (Port 3001)
- Celery Worker
- Celery Beat Scheduler
- Database & Redis (Docker)

## 📋 Manuell start (individuella kommandon)

### Starta backend
**PowerShell/Windows:**
```powershell
cd apps/backend-api
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Mac/Linux:**
```bash
cd apps/backend-api
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Starta frontend (app)
**PowerShell/Windows:**
```powershell
cd apps/app-frontend
npm start
```

**Mac/Linux:**
```bash
cd apps/app-frontend
npm start
```

### Starta frontend (admin)
**PowerShell/Windows:**
```powershell
cd apps/admin-frontend
npm start
```

**Mac/Linux:**
```bash
cd apps/admin-frontend
npm start
```

### Celery Worker
**PowerShell/Windows:**
```powershell
cd apps/backend-api
celery -A app.celery_app worker --loglevel=info
```

**Mac/Linux:**
```bash
cd apps/backend-api
celery -A app.celery_app worker --loglevel=info
```

### Celery Beat Scheduler
**PowerShell/Windows:**
```powershell
cd apps/backend-api
celery -A app.celery_app beat --loglevel=info
```

**Mac/Linux:**
```bash
cd apps/backend-api
celery -A app.celery_app beat --loglevel=info
```

### Starta bara databas och Redis

**PowerShell/Windows:**
```powershell
docker-compose up postgres redis -d
```

**Mac/Linux:**
```bash
docker-compose up postgres redis -d
```

## 🌐 Tillgängliga URLs

- **App Frontend**: http://localhost:3000
- **Admin Frontend**: http://localhost:3001
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Redoc Documentation**: http://localhost:8000/redoc

## 🔍 Testa nya Integration Settings

1. Gå till http://localhost:3000
2. Logga in på ditt konto
3. Navigera till **Settings > Integrations**
4. Testa att:
   - Skapa nya integrations (Fortnox, eEkonomi, HTTP)
   - Konfigurera schemaläggning
   - Köra manuell sync
   - Aktivera/inaktivera schema
