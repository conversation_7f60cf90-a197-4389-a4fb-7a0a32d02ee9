@echo off
REM Aggie Development Server Startup Script (Windows Batch)
echo 🚀 Starting Aggie Development Environment...
echo ==================================================

REM Check if we're in the right directory
if not exist "apps\backend-api" (
    echo ❌ Error: Please run this script from the aggie root directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Start database and Redis
echo 🐳 Starting Database and Redis...
docker-compose up db redis -d
timeout /t 3 /nobreak >nul

REM Start Backend API
echo 🔄 Starting Backend API...
start "Backend API (Port 8000)" cmd /k "cd apps\backend-api && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
timeout /t 2 /nobreak >nul

REM Start App Frontend
echo 🔄 Starting App Frontend...
start "App Frontend (Port 3000)" cmd /k "cd apps\app-frontend && npm start"
timeout /t 2 /nobreak >nul

REM Start Admin Frontend
echo 🔄 Starting Admin Frontend...
start "Admin Frontend (Port 3001)" cmd /k "cd apps\admin-frontend && npm start"
timeout /t 2 /nobreak >nul

REM Start Celery Worker
echo 🔄 Starting Celery Worker...
start "Celery Worker" cmd /k "cd apps\backend-api && celery -A app.celery_app worker --loglevel=info"
timeout /t 2 /nobreak >nul

REM Start Celery Beat
echo 🔄 Starting Celery Beat...
start "Celery Beat Scheduler" cmd /k "cd apps\backend-api && celery -A app.celery_app beat --loglevel=info"

echo.
echo 🎉 All development servers are starting!
echo.
echo 📋 Services Overview:
echo   🌐 App Frontend:      http://localhost:3000
echo   🔧 Admin Frontend:    http://localhost:3001
echo   🚀 Backend API:       http://localhost:8000
echo   📚 API Docs:          http://localhost:8000/docs
echo   🐳 Database:          localhost:5432
echo   🔴 Redis:             localhost:6379
echo.
echo 💡 Tips:
echo   • Each service runs in its own command window
echo   • Close command windows to stop individual services
echo   • Check each window for startup logs and errors
echo.
echo 🔍 To test the new Integration Settings:
echo   1. Go to http://localhost:3000
echo   2. Login to your account
echo   3. Navigate to Settings ^> Integrations
echo   4. Test creating integrations and manual sync
echo.
echo Press any key to exit this script (services will continue running)...
pause >nul
