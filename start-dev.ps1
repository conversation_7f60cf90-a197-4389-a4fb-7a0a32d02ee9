# Aggie Development Server Startup Script
# This script starts all development servers in separate terminal windows

Write-Host "🚀 Starting Aggie Development Environment..." -ForegroundColor Green
Write-Host "=" * 50

# Check if we're in the right directory
if (-not (Test-Path "apps/backend-api") -or -not (Test-Path "apps/app-frontend")) {
    Write-Host "❌ Error: Please run this script from the aggie root directory" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
    exit 1
}

# Function to start a new terminal with a command
function Start-DevServer {
    param(
        [string]$Title,
        [string]$Command,
        [string]$WorkingDirectory = (Get-Location)
    )
    
    Write-Host "🔄 Starting $Title..." -ForegroundColor Cyan
    
    # Start new PowerShell window with the command
    Start-Process powershell -ArgumentList @(
        "-NoExit",
        "-Command",
        "cd '$WorkingDirectory'; Write-Host '🚀 $Title' -ForegroundColor Green; Write-Host 'Working Directory: $(Get-Location)' -ForegroundColor Yellow; $Command"
    ) -WindowStyle Normal
    
    Start-Sleep -Seconds 2
}

# Start database and Redis (Docker)
Write-Host "🐳 Starting Database and Redis..." -ForegroundColor Cyan
try {
    docker-compose up postgres redis -d
    Write-Host "✅ Database and Redis started" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Warning: Could not start Docker services. Make sure Docker is running." -ForegroundColor Yellow
    Write-Host "You may need to start PostgreSQL and Redis manually." -ForegroundColor Yellow
}

Start-Sleep -Seconds 3

# Start Backend API
Start-DevServer -Title "Backend API (Port 8000)" -Command "cd apps/backend-api; python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

# Start Frontend App
Start-DevServer -Title "App Frontend (Port 3000)" -Command "cd apps/app-frontend; npm start"

# Start Admin Frontend
Start-DevServer -Title "Admin Frontend (Port 3001)" -Command "cd apps/admin-frontend; npm start"

# Start Celery Worker
Start-DevServer -Title "Celery Worker" -Command "cd apps/backend-api; celery -A app.celery_app worker --loglevel=info"

# Start Celery Beat
Start-DevServer -Title "Celery Beat Scheduler" -Command "cd apps/backend-api; celery -A app.celery_app beat --loglevel=info"

Write-Host ""
Write-Host "🎉 All development servers are starting!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Services Overview:" -ForegroundColor Yellow
Write-Host "  🌐 App Frontend:      http://localhost:3000" -ForegroundColor White
Write-Host "  🔧 Admin Frontend:    http://localhost:3001" -ForegroundColor White
Write-Host "  🚀 Backend API:       http://localhost:8000" -ForegroundColor White
Write-Host "  📚 API Docs:          http://localhost:8000/docs" -ForegroundColor White
Write-Host "  🐳 Database:          localhost:5432" -ForegroundColor White
Write-Host "  🔴 Redis:             localhost:6379" -ForegroundColor White
Write-Host ""
Write-Host "💡 Tips:" -ForegroundColor Yellow
Write-Host "  • Each service runs in its own terminal window" -ForegroundColor White
Write-Host "  • Close terminal windows to stop individual services" -ForegroundColor White
Write-Host "  • Check each terminal for startup logs and errors" -ForegroundColor White
Write-Host "  • Use Ctrl+C in each terminal to stop services gracefully" -ForegroundColor White
Write-Host ""
Write-Host "🔍 To test the new Integration Settings:" -ForegroundColor Cyan
Write-Host "  1. Go to http://localhost:3000" -ForegroundColor White
Write-Host "  2. Login to your account" -ForegroundColor White
Write-Host "  3. Navigate to Settings > Integrations" -ForegroundColor White
Write-Host "  4. Test creating integrations and manual sync" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to exit this script (services will continue running)..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
