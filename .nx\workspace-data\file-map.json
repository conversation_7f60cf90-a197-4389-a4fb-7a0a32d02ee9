{"version": "6.0", "nxVersion": "21.3.11", "pathMappings": {"@aggie/ui-components": ["packages/ui-components/src/index.ts"], "@aggie/shared-types": ["packages/shared-types/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": "README-MONOREPO.md", "hash": "14405170070264619214"}, {"file": "README.md", "hash": "7340930337580478741"}, {"file": "docker-compose.prod.yml", "hash": "4987633364360680466"}, {"file": "docs/DEPLOYMENT.md", "hash": "867446732787071714"}, {"file": ".env.example", "hash": "6325374848775318276"}, {"file": "docs/API.md", "hash": "7220424936992318662"}, {"file": "tsconfig.base.json", "hash": "1106845473121590863"}, {"file": "nginx.conf", "hash": "3827895090454418577"}, {"file": "nx.json", "hash": "7943974361133357733"}, {"file": "docs/INTEGRATIONS.md", "hash": "15264511742675681405"}, {"file": ".giti<PERSON>re", "hash": "184019514917621119"}, {"file": "init-db.sql", "hash": "6692502442178837968"}, {"file": "package.json", "hash": "16003416251037700812"}, {"file": "docs/ARCHITECTURE.md", "hash": "4606996749694590355"}, {"file": "docker-compose.yml", "hash": "14119113805149745528"}, {"file": ".github/instructions/nx.instructions.md", "hash": "2587759535909921375"}, {"file": "docs/DEVELOPMENT.md", "hash": "9208299406763700867"}, {"file": "docker-compose.dev.yml", "hash": "1468530987692392535"}, {"file": "<PERSON><PERSON><PERSON>", "hash": "2537226200020467983"}, {"file": "package-lock.json", "hash": "6206680188960919387"}], "projectFileMap": {"shared-types": [{"file": "packages/shared-types/package.json", "hash": "17293241752369983537", "deps": ["npm:typescript"]}, {"file": "packages/shared-types/project.json", "hash": "4208504630303651189"}, {"file": "packages/shared-types/src/index.ts", "hash": "10460902349433359965"}, {"file": "packages/shared-types/src/types/index.ts", "hash": "2521834620632379610"}, {"file": "packages/shared-types/tsconfig.lib.json", "hash": "14481697337928466754"}], "admin-frontend": [{"file": "apps/admin-frontend/Dockerfile", "hash": "16887350732645855918"}, {"file": "apps/admin-frontend/Dockerfile.prod", "hash": "8581775582639739374"}, {"file": "apps/admin-frontend/nginx.conf", "hash": "2486793215894162773"}, {"file": "apps/admin-frontend/package-lock.json", "hash": "7684299458499068964"}, {"file": "apps/admin-frontend/package.json", "hash": "13304616465439563853", "deps": ["npm:@types/qrcode.react", "ui-components", "shared-types", "npm:@types/node@20.19.9", "npm:@types/react", "npm:@types/react-dom", "npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:react-router-dom", "npm:react-scripts", "npm:typescript@4.9.5", "npm:axios", "npm:react-hook-form", "npm:react-query", "npm:@headlessui/react", "npm:@heroicons/react", "npm:tailwindcss", "npm:autoprefixer", "npm:postcss", "npm:qrcode.react", "npm:react-hot-toast", "npm:@testing-library/jest-dom", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:@tailwindcss/forms"]}, {"file": "apps/admin-frontend/postcss.config.js", "hash": "1579725330904862112"}, {"file": "apps/admin-frontend/project.json", "hash": "17560911505815870571"}, {"file": "apps/admin-frontend/public/index.html", "hash": "17879939718833079058"}, {"file": "apps/admin-frontend/src/App.tsx", "hash": "4464613650970339519", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/SimpleApp.tsx", "hash": "4237033659271321490", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/__tests__/AuthContext.test.tsx", "hash": "12040871057496173520", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/__tests__/LoginPage.test.tsx", "hash": "13350870590789170105", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/components/Layout.tsx", "hash": "6931554571059759077", "deps": ["npm:react@18.3.1", "npm:react-router-dom", "npm:@headlessui/react", "npm:@heroicons/react"]}, {"file": "apps/admin-frontend/src/contexts/AuthContext.tsx", "hash": "9722001446352333396", "deps": ["npm:react@18.3.1", "npm:react-hot-toast"]}, {"file": "apps/admin-frontend/src/contexts/TenantContext.tsx", "hash": "9708045447128908387", "deps": ["npm:react@18.3.1"]}, {"file": "apps/admin-frontend/src/index.css", "hash": "6060890077207927115"}, {"file": "apps/admin-frontend/src/index.tsx", "hash": "12967885175928185568", "deps": ["npm:react", "npm:react-dom"]}, {"file": "apps/admin-frontend/src/pages/ActionItemsPage.tsx", "hash": "15426964114520051739", "deps": ["npm:react@18.3.1", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/admin-frontend/src/pages/DashboardPage.tsx", "hash": "10474820234636222236", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/pages/InvoicesPage.tsx", "hash": "11954032013869823464", "deps": ["npm:react@18.3.1", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/admin-frontend/src/pages/LoginPage.tsx", "hash": "11449201771330526501", "deps": ["npm:react@18.3.1", "npm:react-router-dom", "npm:react-hook-form", "npm:@heroicons/react"]}, {"file": "apps/admin-frontend/src/pages/SettingsPage.tsx", "hash": "8480423347325354588", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/pages/TwoFAVerifyPage.tsx", "hash": "17330429575126296014", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/services/api.ts", "hash": "6715613632198280895", "deps": ["npm:axios"]}, {"file": "apps/admin-frontend/src/setupTests.ts", "hash": "16903885983588411080"}, {"file": "apps/admin-frontend/src/types/index.ts", "hash": "8252561017891032678"}, {"file": "apps/admin-frontend/tailwind.config.js", "hash": "12413894931608446202"}, {"file": "apps/admin-frontend/tsconfig.app.json", "hash": "17310363877181359515"}, {"file": "apps/admin-frontend/tsconfig.json", "hash": "6764713495435129772"}], "app-frontend": [{"file": "apps/app-frontend/Dockerfile", "hash": "13945634355056200228"}, {"file": "apps/app-frontend/Dockerfile.prod", "hash": "8581775582639739374"}, {"file": "apps/app-frontend/nginx.conf", "hash": "2486793215894162773"}, {"file": "apps/app-frontend/package-lock.json", "hash": "7684299458499068964"}, {"file": "apps/app-frontend/package.json", "hash": "17015359243173538129", "deps": ["npm:@types/qrcode.react", "ui-components", "shared-types", "npm:@types/node@20.19.9", "npm:@types/react", "npm:@types/react-dom", "npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:react-router-dom", "npm:react-scripts", "npm:typescript@4.9.5", "npm:axios", "npm:react-hook-form", "npm:react-query", "npm:@headlessui/react", "npm:@heroicons/react", "npm:tailwindcss", "npm:autoprefixer", "npm:postcss", "npm:qrcode.react", "npm:react-hot-toast", "npm:@testing-library/jest-dom", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:@tailwindcss/forms"]}, {"file": "apps/app-frontend/postcss.config.js", "hash": "1579725330904862112"}, {"file": "apps/app-frontend/project.json", "hash": "3540457525021900959"}, {"file": "apps/app-frontend/public/index.html", "hash": "17879939718833079058"}, {"file": "apps/app-frontend/src/App.tsx", "hash": "4464613650970339519", "deps": ["npm:react"]}, {"file": "apps/app-frontend/src/SimpleApp.tsx", "hash": "4237033659271321490", "deps": ["npm:react"]}, {"file": "apps/app-frontend/src/__tests__/AuthContext.test.tsx", "hash": "12040871057496173520", "deps": ["npm:react"]}, {"file": "apps/app-frontend/src/__tests__/LoginPage.test.tsx", "hash": "13350870590789170105", "deps": ["npm:react"]}, {"file": "apps/app-frontend/src/appCustomStyles.css", "hash": "12263848002426757233"}, {"file": "apps/app-frontend/src/components/Layout.tsx", "hash": "5032761968835120723", "deps": ["npm:react", "npm:react-router-dom", "npm:@headlessui/react", "npm:@heroicons/react"]}, {"file": "apps/app-frontend/src/contexts/AuthContext.tsx", "hash": "9722001446352333396", "deps": ["npm:react@18.3.1", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/contexts/TenantContext.tsx", "hash": "9708045447128908387", "deps": ["npm:react@18.3.1"]}, {"file": "apps/app-frontend/src/index.css", "hash": "6060890077207927115"}, {"file": "apps/app-frontend/src/index.tsx", "hash": "12967885175928185568", "deps": ["npm:react", "npm:react-dom"]}, {"file": "apps/app-frontend/src/pages/ActionItemsPage.tsx", "hash": "6875248684016584818", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/pages/DashboardPage.tsx", "hash": "7786536646140486486", "deps": ["npm:react", "npm:react-query", "npm:react-router-dom", "npm:@heroicons/react"]}, {"file": "apps/app-frontend/src/pages/InvoicesPage.tsx", "hash": "5793566028147698379", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/pages/LoginPage.tsx", "hash": "1918200129088481443", "deps": ["npm:react", "npm:react-router-dom", "npm:react-hook-form", "npm:@heroicons/react"]}, {"file": "apps/app-frontend/src/pages/SettingsPage.tsx", "hash": "7156324571442028959", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:qrcode.react", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/pages/TwoFAVerifyPage.tsx", "hash": "3417118991396930122", "deps": ["npm:react", "npm:react-router-dom", "npm:react-hook-form"]}, {"file": "apps/app-frontend/src/services/api.ts", "hash": "6715613632198280895", "deps": ["npm:axios"]}, {"file": "apps/app-frontend/src/setupTests.ts", "hash": "16903885983588411080"}, {"file": "apps/app-frontend/src/types/index.ts", "hash": "8252561017891032678"}, {"file": "apps/app-frontend/tailwind.config.js", "hash": "216367631106278632"}, {"file": "apps/app-frontend/tsconfig.app.json", "hash": "17310363877181359515"}, {"file": "apps/app-frontend/tsconfig.json", "hash": "6764713495435129772"}], "backend-api": [{"file": "apps/backend-api/Dockerfile", "hash": "1812835409021166104"}, {"file": "apps/backend-api/Dockerfile.prod", "hash": "13305463827139471939"}, {"file": "apps/backend-api/PACKAGE_CHANGES.md", "hash": "228029401836098425"}, {"file": "apps/backend-api/alembic/env.py", "hash": "2630122021204022997"}, {"file": "apps/backend-api/alembic/script.py.mako", "hash": "10069961326131889856"}, {"file": "apps/backend-api/alembic/versions/001_install_pgvector.py", "hash": "9028612211006308560"}, {"file": "apps/backend-api/alembic/versions/002_create_tables.py", "hash": "4992486699753754871"}, {"file": "apps/backend-api/alembic/versions/003_enable_rls.py", "hash": "15191746906346635470"}, {"file": "apps/backend-api/alembic/versions/004_seed_roles.py", "hash": "7450986320602097145"}, {"file": "apps/backend-api/alembic/versions/005_add_foreign_keys.py", "hash": "139565071511276848"}, {"file": "apps/backend-api/alembic/versions/006_update_role_permissions.py", "hash": "1170032471955284379"}, {"file": "apps/backend-api/alembic/versions/007_add_invoice_integrations.py", "hash": "14932377457123667242"}, {"file": "apps/backend-api/alembic/versions/008_fix_encryption_configuration.py", "hash": "7992255627680506041"}, {"file": "apps/backend-api/alembic/versions/009_temporarily_disable_encryption.py", "hash": "16730165890753008677"}, {"file": "apps/backend-api/alembic/versions/010_enable_proper_encryption.py", "hash": "4881726145486818994"}, {"file": "apps/backend-api/alembic/versions/add_ai_agent_tables.py", "hash": "14915688413417844794"}, {"file": "apps/backend-api/alembic/versions/update_action_items_for_ai_agent.py", "hash": "18003814173566253497"}, {"file": "apps/backend-api/app/__init__.py", "hash": "8530932077689354787"}, {"file": "apps/backend-api/app/api/v1/ai_agent.py", "hash": "8607059176220251511"}, {"file": "apps/backend-api/app/celery_app.py", "hash": "10551780997406360417"}, {"file": "apps/backend-api/app/config.py", "hash": "1232742012975300877"}, {"file": "apps/backend-api/app/database.py", "hash": "12952241032989881814"}, {"file": "apps/backend-api/app/integrations/__init__.py", "hash": "2249975506754415871"}, {"file": "apps/backend-api/app/integrations/base.py", "hash": "14038749171068650457"}, {"file": "apps/backend-api/app/integrations/fortnox.py", "hash": "7539434295574265860"}, {"file": "apps/backend-api/app/integrations/simple_http.py", "hash": "1471125474146439864"}, {"file": "apps/backend-api/app/integrations/visma.py", "hash": "13888545133839108741"}, {"file": "apps/backend-api/app/main.py", "hash": "16090335987879115678"}, {"file": "apps/backend-api/app/middleware.py", "hash": "16503142604741694655"}, {"file": "apps/backend-api/app/models/__init__.py", "hash": "13114107264540086554"}, {"file": "apps/backend-api/app/models/action_item.py", "hash": "13488239977046911320"}, {"file": "apps/backend-api/app/models/agent_session.py", "hash": "1503252999325454135"}, {"file": "apps/backend-api/app/models/base.py", "hash": "5116069819379370795"}, {"file": "apps/backend-api/app/models/integration.py", "hash": "7353828091350235831"}, {"file": "apps/backend-api/app/models/invoice.py", "hash": "9573975080708735545"}, {"file": "apps/backend-api/app/models/tenant.py", "hash": "9052775922477082092"}, {"file": "apps/backend-api/app/models/user.py", "hash": "13708580356275322886"}, {"file": "apps/backend-api/app/routers/__init__.py", "hash": "8173539746521203630"}, {"file": "apps/backend-api/app/routers/action_items.py", "hash": "10913156794129885316"}, {"file": "apps/backend-api/app/routers/auth.py", "hash": "1631067169429010536"}, {"file": "apps/backend-api/app/routers/integrations.py", "hash": "10241903613937462440"}, {"file": "apps/backend-api/app/routers/invoices.py", "hash": "13697175667721519368"}, {"file": "apps/backend-api/app/routers/users.py", "hash": "7124737173145318533"}, {"file": "apps/backend-api/app/schemas/__init__.py", "hash": "4915467477891199470"}, {"file": "apps/backend-api/app/schemas/ai_agent.py", "hash": "15071314775780579509"}, {"file": "apps/backend-api/app/schemas/auth.py", "hash": "7920929271226656172"}, {"file": "apps/backend-api/app/schemas/integration.py", "hash": "11356743581854202112"}, {"file": "apps/backend-api/app/schemas/user_example.py", "hash": "8827497842783416691"}, {"file": "apps/backend-api/app/services/__init__.py", "hash": "18032459416124082886"}, {"file": "apps/backend-api/app/services/ai_agent_action_items.py", "hash": "4984872036262278840"}, {"file": "apps/backend-api/app/services/ai_agent_orchestrator.py", "hash": "17623635379457632276"}, {"file": "apps/backend-api/app/services/ai_agent_tools.py", "hash": "17857563474500459334"}, {"file": "apps/backend-api/app/services/duplicate_detection.py", "hash": "4637772044248233436"}, {"file": "apps/backend-api/app/services/integration_service.py", "hash": "2013055516849726318"}, {"file": "apps/backend-api/app/services/llm_provider.py", "hash": "17636970096084757028"}, {"file": "apps/backend-api/app/services/ocr_service.py", "hash": "17197855778081982005"}, {"file": "apps/backend-api/app/services/vector_service.py", "hash": "11825299037056913429"}, {"file": "apps/backend-api/app/tasks/__init__.py", "hash": "8480902079378511937"}, {"file": "apps/backend-api/app/tasks/ai_agent_tasks.py", "hash": "162077130097273745"}, {"file": "apps/backend-api/app/tasks/erp_integration.py", "hash": "13738713398402976647"}, {"file": "apps/backend-api/app/tasks/notifications.py", "hash": "3572232295763718439"}, {"file": "apps/backend-api/app/utils/__init__.py", "hash": "6648062285855471645"}, {"file": "apps/backend-api/app/utils/email_validator.py", "hash": "1948687087061748247"}, {"file": "apps/backend-api/app/utils/encryption.py", "hash": "7658299233970602102"}, {"file": "apps/backend-api/app/utils/oauth2.py", "hash": "13249036402726177201"}, {"file": "apps/backend-api/app/utils/permissions.py", "hash": "5313430553303445620"}, {"file": "apps/backend-api/app/utils/security.py", "hash": "822232487133557567"}, {"file": "apps/backend-api/celerybeat-schedule", "hash": "15082945579400802802"}, {"file": "apps/backend-api/create_demo_user.py", "hash": "1929378992874166194"}, {"file": "apps/backend-api/docs/AI_AGENT_SYSTEM.md", "hash": "1340556239953172763"}, {"file": "apps/backend-api/docs/GOOGLE_SEARCH_SETUP.md", "hash": "907603680777050265"}, {"file": "apps/backend-api/docs/MIGRATION_GUIDE.md", "hash": "10771489814783271730"}, {"file": "apps/backend-api/project.json", "hash": "15776168066986678026"}, {"file": "apps/backend-api/pytest.ini", "hash": "3585287711390049353"}, {"file": "apps/backend-api/requirements.txt", "hash": "5178304662548245784"}, {"file": "apps/backend-api/temp-nx/aggie/.gitignore", "hash": "18435103667633526488"}, {"file": "apps/backend-api/temp-nx/aggie/README.md", "hash": "5765815396896190387"}, {"file": "apps/backend-api/temp-nx/aggie/nx.json", "hash": "7984885312398650396"}, {"file": "apps/backend-api/temp-nx/aggie/package.json", "hash": "1880928286645203938"}, {"file": "apps/backend-api/temp-nx/aggie/packages/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/backend-api/tests/__init__.py", "hash": "10530555391305414344"}, {"file": "apps/backend-api/tests/conftest.py", "hash": "1305389926311210674"}, {"file": "apps/backend-api/tests/test_ai_agent_integration.py", "hash": "13625310635595314854"}, {"file": "apps/backend-api/tests/test_ai_agent_orchestrator.py", "hash": "6111112444851136802"}, {"file": "apps/backend-api/tests/test_auth.py", "hash": "18309435454768705847"}, {"file": "apps/backend-api/tests/test_duplicate_detection.py", "hash": "18016419937165622483"}, {"file": "apps/backend-api/tests/test_integrations.py", "hash": "17566668324631171930"}, {"file": "apps/backend-api/tests/test_invoices.py", "hash": "9975863405474238176"}, {"file": "apps/backend-api/tests/test_services.py", "hash": "8977645068478444163"}, {"file": "apps/backend-api/update_permissions.py", "hash": "8380590866922075405"}], "ui-components": [{"file": "packages/ui-components/package.json", "hash": "551472291336843318", "deps": ["npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:@types/react", "npm:@types/react-dom", "npm:typescript", "npm:@headlessui/react", "npm:@heroicons/react"]}, {"file": "packages/ui-components/project.json", "hash": "11983930012854116052"}, {"file": "packages/ui-components/src/components/index.ts", "hash": "1145998184831838660"}, {"file": "packages/ui-components/src/components/utils.js", "hash": "574870991431321524"}, {"file": "packages/ui-components/src/index.ts", "hash": "805708636791503367"}, {"file": "packages/ui-components/tsconfig.lib.json", "hash": "14481697337928466754"}]}}}