import React, { useState } from 'react';
import { useQuery, useMutation } from 'react-query';
import { ShieldCheckIcon, KeyIcon } from '@heroicons/react/24/outline';
import QRCode from 'qrcode.react';
import { authApi } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useTenant } from '../contexts/TenantContext';
import toast from 'react-hot-toast';
import '../appCustomStyles.css';

export default function SettingsPage() {
  const { user, refreshUser } = useAuth();
  const { currentTenant } = useTenant();
  const [activeTab, setActiveTab] = useState('security');

  if (!currentTenant) {
    return (
      <div className="page-background">
        <div className="content-container">
          <div className="card-container text-center">
            <p className="text-gray-500">Please select a tenant to view settings.</p>
          </div>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'account', name: 'Account', icon: KeyIcon },
  ];

  return (
    <div className="page-background">
      <div className="content-container">
        <div className="card-container-large mb-8">
          <h1 className="page-title custom-gradient-text">Settings</h1>
          <p className="text-gray-700">
            Manage your account and security settings
          </p>
        </div>

        <div className="card-container mb-8">
          <div className="sm:hidden">
            <select
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              className="select-custom"
            >
              {tabs.map((tab) => (
                <option key={tab.id} value={tab.id}>
                  {tab.name}
                </option>
              ))}
            </select>
          </div>
          <div className="hidden sm:block">
            <nav className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-indigo-500 text-indigo-600 bg-gradient-to-r from-indigo-50 to-purple-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                  } whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm flex items-center rounded-t-2xl transition-all`}
                >
                  <tab.icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        <div>
          {activeTab === 'security' && <SecuritySettings user={user} onUserUpdate={refreshUser} />}
          {activeTab === 'account' && <AccountSettings user={user} tenant={currentTenant} />}
        </div>
      </div>
    </div>
  );
}

function SecuritySettings({ user, onUserUpdate }: { user: any; onUserUpdate: () => void }) {
  const [showSetup, setShowSetup] = useState(false);
  const [setupData, setSetupData] = useState<any>(null);
  const [verificationCode, setVerificationCode] = useState('');

  const setupMutation = useMutation(authApi.setupTwoFA, {
    onSuccess: (data) => {
      setSetupData(data);
      setShowSetup(true);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to setup 2FA');
    },
  });

  const enableMutation = useMutation(
    (code: string) => authApi.enableTwoFA(code),
    {
      onSuccess: () => {
        setShowSetup(false);
        setSetupData(null);
        setVerificationCode('');
        onUserUpdate();
        toast.success('Two-factor authentication enabled!');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to enable 2FA');
      },
    }
  );

  const disableMutation = useMutation(
    ({ password, code }: { password: string; code: string }) =>
      authApi.disableTwoFA(password, code),
    {
      onSuccess: () => {
        onUserUpdate();
        toast.success('Two-factor authentication disabled!');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to disable 2FA');
      },
    }
  );

  return (
    <div className="space-y-6">
      <div className="card-container">
        <h3 className="section-title flex items-center">
          <ShieldCheckIcon className="h-6 w-6 mr-2 text-indigo-600" />
          Two-Factor Authentication
        </h3>

        {user?.is_2fa_enabled ? (
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6">
            <div className="flex items-center mb-4">
              <ShieldCheckIcon className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-sm text-green-700 font-medium">
                Two-factor authentication is enabled
              </span>
            </div>
            <DisableTwoFAForm onSubmit={disableMutation.mutate} isLoading={disableMutation.isLoading} />
          </div>
        ) : (
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6">
            <p className="text-sm text-gray-600 mb-4">
              Add an extra layer of security to your account by enabling two-factor authentication.
            </p>
            <button
              onClick={() => setupMutation.mutate()}
              disabled={setupMutation.isLoading}
              className="btn-small"
            >
              {setupMutation.isLoading ? 'Setting up...' : 'Enable 2FA'}
            </button>
          </div>
        )}
      </div>

      {/* 2FA Setup Modal */}
      {showSetup && setupData && (
        <TwoFASetupModal
          setupData={setupData}
          verificationCode={verificationCode}
          setVerificationCode={setVerificationCode}
          onEnable={() => enableMutation.mutate(verificationCode)}
          onCancel={() => {
            setShowSetup(false);
            setSetupData(null);
            setVerificationCode('');
          }}
          isLoading={enableMutation.isLoading}
        />
      )}
    </div>
  );
}

function DisableTwoFAForm({ onSubmit, isLoading }: { onSubmit: (data: any) => void; isLoading: boolean }) {
  const [password, setPassword] = useState('');
  const [code, setCode] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ password, code });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
          Current Password
        </label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="input-custom"
        />
      </div>
      <div>
        <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-2">
          2FA Code
        </label>
        <input
          type="text"
          id="code"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          maxLength={6}
          required
          className="input-custom text-center"
          placeholder="000000"
        />
      </div>
      <button
        type="submit"
        disabled={isLoading}
        className="btn-small bg-red-600 hover:bg-red-700"
      >
        {isLoading ? 'Disabling...' : 'Disable 2FA'}
      </button>
    </form>
  );
}

function TwoFASetupModal({ setupData, verificationCode, setVerificationCode, onEnable, onCancel, isLoading }: any) {
  return (
    <div className="fixed inset-0 z-10 overflow-y-auto">
      <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onCancel} />

        <div className="inline-block transform overflow-hidden rounded-3xl bg-white bg-opacity-95 px-6 pt-6 pb-6 text-left align-bottom shadow-2xl border border-purple-200 transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-8 sm:align-middle">
          <div>
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-indigo-100 to-purple-100">
              <ShieldCheckIcon className="h-8 w-8 text-indigo-600" />
            </div>
            <div className="mt-4 text-center sm:mt-6">
              <h3 className="text-xl font-semibold leading-6 text-gray-900 custom-gradient-text">
                Setup Two-Factor Authentication
              </h3>
              <div className="mt-6 bg-gradient-to-r from-purple-50 via-indigo-50 to-pink-50 rounded-2xl p-6">
                <p className="text-sm text-gray-600 mb-4 text-center">
                  Scan this QR code with your authenticator app:
                </p>
                <div className="flex justify-center mb-6">
                  <div className="bg-white p-4 rounded-2xl shadow-sm">
                    <QRCode value={setupData.qr_code} size={200} />
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-4 text-center">
                  Or enter this secret manually: <code className="bg-white px-3 py-1 rounded-xl font-mono text-xs">{setupData.secret}</code>
                </p>
                <div className="mb-6">
                  <label htmlFor="verification" className="block text-sm font-medium text-gray-700 mb-2">
                    Enter verification code:
                  </label>
                  <input
                    type="text"
                    id="verification"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    maxLength={6}
                    className="input-custom text-center text-lg tracking-widest"
                    placeholder="000000"
                  />
                </div>
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl p-4 mb-4">
                  <p className="text-sm text-yellow-800 font-semibold mb-2">Recovery Codes</p>
                  <p className="text-xs text-yellow-700 mb-3">
                    Save these recovery codes in a safe place. You can use them to access your account if you lose your authenticator device.
                  </p>
                  <div className="grid grid-cols-2 gap-2 text-xs font-mono">
                    {setupData.backup_codes.map((code: string, index: number) => (
                      <div key={index} className="bg-white p-2 rounded-xl border shadow-sm">
                        {code}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 sm:mt-8 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-4">
            <button
              type="button"
              onClick={onEnable}
              disabled={!verificationCode || verificationCode.length !== 6 || isLoading}
              className="btn-primary sm:col-start-2"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Enabling...
                </div>
              ) : (
                'Enable 2FA'
              )}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary mt-3 sm:col-start-1 sm:mt-0"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function AccountSettings({ user, tenant }: { user: any; tenant: any }) {
  return (
    <div className="space-y-6">
      <div className="card-container">
        <h3 className="section-title flex items-center">
          <KeyIcon className="h-6 w-6 mr-2 text-indigo-600" />
          Account Information
        </h3>
        <div className="bg-gradient-to-r from-purple-50 via-indigo-50 to-pink-50 rounded-2xl p-6">
          <dl className="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">Email</dt>
              <dd className="mt-1 text-sm font-semibold text-gray-900">{user?.email}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Current Tenant</dt>
              <dd className="mt-1 text-sm font-semibold text-gray-900">{tenant?.name}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Role</dt>
              <dd className="mt-1 text-sm font-semibold text-gray-900">{tenant?.role}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">2FA Status</dt>
              <dd className="mt-1 text-sm font-semibold">
                {user?.is_2fa_enabled ? (
                  <span className="badge-success">Enabled</span>
                ) : (
                  <span className="badge-error">Disabled</span>
                )}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <div className="card-container">
        <h3 className="section-title">Permissions</h3>
        <div className="bg-gradient-to-r from-purple-50 via-indigo-50 to-pink-50 rounded-2xl p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {tenant?.permissions?.map((permission: string) => (
              <span
                key={permission}
                className="badge-info"
              >
                {permission}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
